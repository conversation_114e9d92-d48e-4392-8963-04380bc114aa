

import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist';
import type { PDFDocumentProxy, RenderTask } from 'pdfjs-dist';
import type { PdfRisk } from '../types/pdf-risk';

// 新增：定义滚动行为类型
type ScrollBehavior = 'auto' | 'smooth';

/**
 * 设置PDF.js worker文件的路径
 * 这是PDF.js库正常工作所必需的
 */
// 修改worker路径设置，提供更可靠的加载方式
try {
  const workerSrc = new URL(
      'pdfjs-dist/build/pdf.worker.mjs',
      import.meta.url
    ).toString();
  
  console.log('设置PDF.js Worker路径:', workerSrc);
  GlobalWorkerOptions.workerSrc = workerSrc;
  
  // 检查worker是否设置
  if (!GlobalWorkerOptions.workerSrc) {
    console.error('警告: PDF.js Worker路径未设置!');
  } else {
    console.log('PDF.js Worker路径已设置为:', GlobalWorkerOptions.workerSrc);
  }
} catch (error) {
  console.error('设置PDF.js Worker路径出错:', error);
}

/**
 * 视口信息接口
 */
interface ViewportInfo {
  width: number;
  height: number;
  originalWidth: number;
  originalHeight: number;
  scale: number;
}

/**
 * PDF查看器类
 * 
 * 功能概述:
 * 1. 加载并显示PDF文件
 * 2. 实现虚拟滚动和页面懒加载
 * 3. 处理文档中的风险标记
 * 4. 提供页面导航和指示器
 * 5. 支持窗口大小调整时的重新渲染
 * 6. 优化内存使用和页面渲染性能
 * 7. 支持键盘导航
 */
export class PDFViewer {
    private container: HTMLElement;                          // PDF容器元素
    private pdfDoc: PDFDocumentProxy | null = null;          // PDF文档对象
    private risks: PdfRisk[] = [];                           // 风险项数组
    private pagesContainer: HTMLElement | null = null;       // 页面容器元素
    private pageElements: HTMLElement[] = [];                // 所有页面元素的数组
    private pageIndicator: HTMLElement | null = null;        // 页面指示器元素
    private hideIndicatorTimeout: number | null = null;      // 隐藏指示器的定时器ID
    private loadedPages: Set<number> = new Set();            // 已加载页面的集合
    // @ts-ignore - 这些变量在原始代码中被使用，我们保留它们以便将来可能的功能恢复
    private isLoadingPage: boolean = false;                  // 页面加载状态标志
    // @ts-ignore - 这些变量在原始代码中被使用，我们保留它们以便将来可能的功能恢复
    private pageLoadQueue: number[] = [];                    // 页面加载队列
    // @ts-ignore - 这些变量在原始代码中被使用，我们保留它们以便将来可能的功能恢复
    private readonly PRELOAD_PAGES = 2;                      // 预加载页数
    private pagesObserver: IntersectionObserver | null = null; // 页面观察器
    private currentRenderTasks: Map<number, RenderTask> = new Map(); // 渲染任务映射
    private scale: number = 1.0;                             // 当前缩放比例
    private outputScale: number = window.devicePixelRatio || 1; // 输出缩放比例(支持高DPI)
    private memoryMonitorInterval: number | null = null;     // 内存监控定时器ID
    private keyboardNavEnabled: boolean = true;              // 键盘导航标志
    private activeRiskId: string | null = null;              // 存储当前激活的风险项ID
    private visibleRiskLevels: string[] | null = null;       // 存储当前应显示的风险等级，null表示显示所有

    /**
     * 构造函数
     * @param pdfPath PDF文件路径
     * @param containerId 容器元素ID
     */
    constructor(pdfPath: string, containerId: string) {
        const container = document.getElementById(containerId);
        if (!container) {
          throw new Error(`Container with id ${containerId} not found`);
        }
        
        this.container = container;

        // 创建页面容器
        this.pagesContainer = document.createElement('div');
        this.pagesContainer.className = 'pdf-pages-container';
        this.container.appendChild(this.pagesContainer);

        this.initPageIndicator();
        this.loadPdf(pdfPath);

        // 启动内存监控
        this.startMemoryMonitoring();
        
        // 添加键盘事件监听
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }

    /**
     * 处理键盘事件
     * @param event 键盘事件
     */
    private handleKeyDown(event: KeyboardEvent): void {
        // 如果禁用了键盘导航或者输入框有焦点，则不处理
        if (!this.keyboardNavEnabled || 
            (document.activeElement && 
             (document.activeElement.tagName === 'INPUT' || 
              document.activeElement.tagName === 'TEXTAREA'))) {
            return;
        }

        switch (event.key) {
            // 翻页控制
            case 'ArrowRight':
            case 'PageDown':
                this.goToNextPage();
                event.preventDefault();
                break;
            case 'ArrowLeft':
            case 'PageUp':
                this.goToPreviousPage();
                event.preventDefault();
                break;
            case 'Home':
                this.goToPage(1);
                event.preventDefault();
                break;
            case 'End':
                if (this.pdfDoc) {
                    this.goToPage(this.pdfDoc.numPages);
                }
                event.preventDefault();
                break;
        }
    }
    
    /**
     * 启用键盘导航
     */
    public enableKeyboardNavigation(): void {
        this.keyboardNavEnabled = true;
    }
    
    /**
     * 禁用键盘导航
     */
    public disableKeyboardNavigation(): void {
        this.keyboardNavEnabled = false;
    }
    
    /**
     * 跳转到下一页
     */
    public goToNextPage(): void {
        const currentPage = this.getCurrentPage();
        if (this.pdfDoc && currentPage < this.pdfDoc.numPages) {
            this.goToPage(currentPage + 1);
        }
    }
    
    /**
     * 跳转到上一页
     */
    public goToPreviousPage(): void {
        const currentPage = this.getCurrentPage();
        if (currentPage > 1) {
            this.goToPage(currentPage - 1);
        }
    }

    /**
     * 启动内存监控
     * 定期检查内存使用情况，在内存压力大时主动清理
     */
    private startMemoryMonitoring(): void {
      // 检查是否支持performance.memory API
      const performanceMemory = (performance as any).memory;
      if (performanceMemory) {
        this.memoryMonitorInterval = window.setInterval(() => {
          // 获取当前内存使用情况
          const memoryUsage = performanceMemory.usedJSHeapSize / performanceMemory.jsHeapSizeLimit;
          const loadedPagesCount = this.loadedPages.size;
          
          // 计算内存压力级别
          let memoryPressure = 'low';
          if (memoryUsage > 0.8) {
            memoryPressure = 'critical';
          } else if (memoryUsage > 0.6) {
            memoryPressure = 'high';
          } else if (memoryUsage > 0.4) {
            memoryPressure = 'medium';
          }
          
          // 根据内存压力级别决定保留页面数量
          let keepPageRange = 10; // 默认值
          
          switch (memoryPressure) {
            case 'critical':
              keepPageRange = 3; // 严重压力下只保留最少页面
              break;
            case 'high':
              keepPageRange = 5; // 高压力下保留较少页面
              break;
            case 'medium':
              keepPageRange = 8; // 中等压力下适度保留
              break;
            // 低压力下保持默认值
          }
          
          // 在非生产环境下记录内存使用情况
          if (process.env.NODE_ENV !== 'production') {
            console.log(`内存监控: 使用率 ${(memoryUsage * 100).toFixed(1)}%, 已加载页面 ${loadedPagesCount}`);
            console.log(`内存压力级别: ${memoryPressure}, 保留页面范围: ${keepPageRange}`);
          }
          
          // 获取当前页面，确保有效
          const currentPage = this.getCurrentPage();
          if (currentPage > 0) {
            // 根据内存压力决定是否执行清理
            if (memoryPressure === 'critical' || 
                (memoryPressure === 'high' && loadedPagesCount > keepPageRange * 2) ||
                (memoryUsage > 0.4 && loadedPagesCount > 15)) {
              
              // 执行清理
              if (process.env.NODE_ENV !== 'production') {
                console.log(`内存使用率高 (${(memoryUsage * 100).toFixed(1)}%)，执行主动清理，保留范围: ${keepPageRange}页`);
              }
              this.cleanUpUnusedPages(currentPage, keepPageRange);
            }
          }
        }, 15000); // 缩短检查间隔到15秒，以便更及时响应内存压力
      }
    }

    /**
     * 停止内存监控
     */
    private stopMemoryMonitoring(): void {
      if (this.memoryMonitorInterval !== null) {
        window.clearInterval(this.memoryMonitorInterval);
        this.memoryMonitorInterval = null;
      }
    }

    /**
     * 加载PDF文件
     * @param pdfPath PDF文件路径
     */
    private async loadPdf(pdfPath: string): Promise<void> {
        try {
          // 显示加载状态
          if (this.pagesContainer) {
            this.pagesContainer.innerHTML = '<div class="pdf-loading">PDF 文件加载中，请稍候...</div>';
          }
          
          // 输出当前PDF.js配置信息，帮助诊断问题
          console.log('PDF.js 配置信息:', {
            'workerSrc设置': GlobalWorkerOptions.workerSrc,
            'PDF路径': pdfPath,
            '设备像素比': window.devicePixelRatio
          });
          
          // 处理 URL 以解决 URL.parse 问题
          let pdfSource;
          try {
            // 尝试将路径字符串转换为标准 URL 对象
            // 这会规避 pdfjs 内部使用 URL.parse 的问题
            if (pdfPath.startsWith('blob:') || pdfPath.startsWith('data:')) {
              // 对于 blob 或 data URL，直接使用
              pdfSource = pdfPath;
            } else if (pdfPath.startsWith('http://') || pdfPath.startsWith('https://') || pdfPath.startsWith('file://')) {
              // 对于绝对 URL，创建 URL 对象
              pdfSource = new URL(pdfPath).toString();
            } else {
              // 对于相对路径，基于当前位置创建 URL
              pdfSource = new URL(pdfPath, window.location.href).toString();
            }
            console.log('处理后的 PDF 路径:', pdfSource);
          } catch (urlError) {
            console.error('URL 处理错误:', urlError);
            // 如果 URL 转换失败，尝试使用原始路径
            pdfSource = pdfPath;
          }
          
          // 尝试加载 PDF，使用处理后的 URL
          this.pdfDoc = await getDocument(pdfSource).promise;
          
          console.log('PDF文档加载成功，总页数:', this.pdfDoc.numPages);
          
          // 初始化页面
          await this.initializePages();
          
          // 设置窗口大小改变事件监听
          window.addEventListener('resize', this.onWindowResize.bind(this));
          
          // 添加滚动事件监听
          if (this.pagesContainer) {
            this.container.addEventListener('scroll', this.onContainerScroll.bind(this));
          }
          
        } catch (error:any) {
          console.error('Error loading PDF:', error);
          
          // 显示用户友好的错误信息
          if (this.pagesContainer) {
            this.pagesContainer.innerHTML = `
              <div class="pdf-error">
                <h3>PDF 加载失败</h3>
                <p>无法加载 PDF 文件，可能的原因：</p>
                <ul>
                  <li>文件路径不正确</li>
                  <li>文件格式损坏</li>
                  <li>文件不存在</li>
                  <li>浏览器无法访问该文件</li>
                  <li>浏览器版本不兼容 (${navigator.userAgent})</li>
                </ul>
                <p>错误详情: ${error.message || '未知错误'}</p>
              </div>
            `;
          }
        }
      }

    /**
     * 初始化页面显示
     * 创建所有页面的占位符并设置观察者
     */
      private async initializePages(): Promise<void> {
        if (!this.pdfDoc || !this.pagesContainer) return;
        
        console.log('开始初始化页面...');
        this.pagesContainer.innerHTML = '';
        this.pageElements = [];
        
        // 获取第一页以计算默认高度比例
        let defaultPageHeight = 1000;
        try {
            // 获取第一页
            const firstPage = await this.pdfDoc.getPage(1);
            // 计算默认高度（基于 A4 比例和容器宽度）
            const viewport = firstPage.getViewport({ scale: 1.0 });
            const containerWidth = this.container.clientWidth;
            this.scale = containerWidth / viewport.width;
            defaultPageHeight = viewport.height * this.scale;
            console.log('计算得到默认页面高度:', defaultPageHeight, '容器宽度:', containerWidth, '缩放比例:', this.scale);
        } catch (error) {
            console.warn('无法获取第一页以计算高度，使用默认高度', error);
        }
        
        console.log(`为${this.pdfDoc.numPages}页创建占位符...`);
        // 创建所有页面的占位符
        for (let i = 1; i <= this.pdfDoc.numPages; i++) {
          // 创建页面容器
          const pageContainer = document.createElement('div');
          pageContainer.className = 'pdf-page-container';
          pageContainer.style.position = 'relative';
          pageContainer.dataset.pageNumber = i.toString();
          
          // 创建页面元素
          const pageDiv = document.createElement('div');
          pageDiv.className = 'pdf-page';
          pageDiv.dataset.pageNumber = i.toString();
          // 使用计算出的默认高度
          pageDiv.style.minHeight = `${defaultPageHeight}px`;
          
          // 将页面添加到页面容器
          pageContainer.appendChild(pageDiv);
          
          // 将页面容器添加到页面总容器
          this.pagesContainer.appendChild(pageContainer);
          
          // 保存元素引用
          this.pageElements.push(pageDiv);
        }
        
        console.log('页面占位符创建完成，初始化页面观察者...');
        // 初始化页面观察者
        this.initPagesObserver();
        
        // 加载初始可见页面
        this.checkVisiblePages();
        console.log('初始化页面完成');
      }
      
    /**
     * 渲染指定页面
     * @param pageNumber 页码
     */
    // @ts-ignore - 该方法在多处被调用，是懒加载功能的核心，保留完整实现
    private async renderPage(pageNumber: number): Promise<void> {
        if (!this.pdfDoc) {
            console.error(`尝试渲染页面 ${pageNumber}, 但PDF文档未加载`);
            return;
        }
        
        console.log(`开始渲染页面 ${pageNumber}...`);
        
        // 获取页面元素
        const pageElement = this.pageElements[pageNumber - 1];
        
        if (!pageElement) {
            console.error(`页面 ${pageNumber} 元素不存在`);
            return;
        }
        
        try {
          // 获取 PDF 页面
          console.log(`从PDF文档获取页面 ${pageNumber}...`);
          const page = await this.pdfDoc.getPage(pageNumber);
          console.log(`获取页面 ${pageNumber} 成功`);
          
          // 计算缩放比例以适应容器宽度
          const viewport = page.getViewport({ scale: 1.0 });
          const containerWidth = pageElement.clientWidth;
          const scale = containerWidth / viewport.width;
          const scaledViewport = page.getViewport({ scale });
          
          console.log(`页面 ${pageNumber} 尺寸计算: 原始尺寸=${viewport.width}x${viewport.height}, 缩放=${scale}, 缩放后=${scaledViewport.width}x${scaledViewport.height}`);
          
          // 支持高DPI屏幕
          const outputScale = this.outputScale;
          
          // 计算Canvas的实际尺寸
          const canvasWidth = Math.floor(scaledViewport.width * outputScale);
          const canvasHeight = Math.floor(scaledViewport.height * outputScale);
          
          console.log(`创建Canvas, 尺寸=${canvasWidth}x${canvasHeight}, outputScale=${outputScale}`);
          // 创建Canvas
          const canvas = document.createElement('canvas');
          canvas.width = canvasWidth;
          canvas.height = canvasHeight;
          
          // 获取2D上下文
          const context = canvas.getContext('2d', { alpha: false });
          if (!context) {
            console.error(`页面 ${pageNumber} 无法创建Canvas上下文`);
            throw new Error('无法创建Canvas上下文');
          }
          
          // 设置Canvas样式尺寸（显示尺寸）
          canvas.style.width = `${Math.floor(scaledViewport.width)}px`;
          canvas.style.height = `${Math.floor(scaledViewport.height)}px`;
          
          // 设置页面元素的高度以匹配渲染的页面
          pageElement.style.height = `${scaledViewport.height}px`;
          pageElement.style.minHeight = `${scaledViewport.height}px`;
          
          console.log(`清空页面 ${pageNumber} 内容并添加Canvas...`);
          // 清除页面内容并添加画布
          pageElement.innerHTML = '';
          pageElement.appendChild(canvas);
          
          // 设置变换矩阵以支持高DPI
          const transform = outputScale !== 1
            ? [outputScale, 0, 0, outputScale, 0, 0]
            : undefined;
          
          console.log(`开始PDF渲染任务, 页面=${pageNumber}...`);
          // 渲染页面
          const renderContext = {
            canvasContext: context,
            viewport: scaledViewport,
            transform
          };
          
          // 存储渲染任务以便在需要时取消
          const renderTask = page.render(renderContext);
          this.currentRenderTasks.set(pageNumber, renderTask);
          
          // 等待渲染完成
          console.log(`等待页面 ${pageNumber} 渲染完成...`);
          await renderTask.promise;
          console.log(`页面 ${pageNumber} 渲染完成!`);
          
          // 渲染完成后移除渲染任务
          this.currentRenderTasks.delete(pageNumber);
          
          // 如果有风险项，渲染风险项标记
          const viewportInfo = {
            width: scaledViewport.width,
            height: scaledViewport.height,
            originalWidth: viewport.width,
            originalHeight: viewport.height,
            scale: scale
          };
          
          console.log(`开始为页面 ${pageNumber} 渲染风险项...`);
          this.renderRisks(pageNumber, viewportInfo);
          console.log(`页面 ${pageNumber} 全部渲染完成`);
          
        } catch (error) {
          console.error(`Error rendering page ${pageNumber}:`, error);
          pageElement.innerHTML = `<div class="pdf-page-error">页面 ${pageNumber} 加载失败</div>`;
        }
    }
      
    /**
     * 渲染页面上的风险项标记
     * @param pageNumber 页码
     * @param viewport 视口信息，包含缩放比例
     */
    private renderRisks(pageNumber: number, viewport: ViewportInfo): void {
        // 如果没有风险项则返回
        if (!this.risks || this.risks.length === 0) return;
        
        // 获取页面元素
        const pageElement = this.pageElements[pageNumber - 1];
        if (!pageElement) return;
        
        // 获取页面容器（父元素）
        const pageContainer = pageElement.parentElement;
        if (!pageContainer) return;

        // 先清除该页面容器中可能存在的旧风险项层
        const existingRiskLayers = pageContainer.querySelectorAll('.pdf-risk-layer');
        existingRiskLayers.forEach(layer => {
            pageContainer.removeChild(layer);
        });


        // 创建风险项层
        const riskLayer = document.createElement('div');
        riskLayer.className = 'pdf-risk-layer';
        riskLayer.style.position = 'absolute';
        riskLayer.style.top = '0';
        riskLayer.style.left = '0';
        riskLayer.style.width = '100%';
        riskLayer.style.height = '100%';
        riskLayer.style.zIndex = '1'; // 确保在Canvas之上

        // 计算缩放比例
        const scale = viewport.scale || 1.0;
        
        // 查找该页面的所有风险项
        const pageIndex = pageNumber;
        let pageRisks = this.risks.filter(risk => {
            return risk.anchors.some(a => a.pageIndex === pageIndex);
        });

        // 如果设置了可见风险等级筛选，则进一步筛选
        if (this.visibleRiskLevels !== null) {
            pageRisks = pageRisks.filter(risk => {
                const riskLevel = risk.riskLevel || (risk as any).level;
                return this.visibleRiskLevels!.includes(riskLevel);
            });
        }
        
        // 如果没有风险项，直接返回
        if (pageRisks.length === 0) return;
        
        // 使用DocumentFragment减少DOM操作，提高性能
        const fragment = document.createDocumentFragment();
        
        // 为每个风险项创建高亮元素
        pageRisks.forEach(risk => {
            // 获取当前页面的锚点
            const pageAnchors = risk.anchors.filter(a => a.pageIndex === pageIndex);
            
            // 处理每个锚点的坐标点
            pageAnchors.forEach(anchor => {
                // 处理锚点中的每组坐标（对角坐标）
                anchor.points.forEach(point => {
                    // 创建高亮元素
                    const highlightElement = document.createElement('div');
                    
                    // 添加基础类名和风险等级类名
                    // 兼容旧的level和新的riskLevel字段
                    const riskLevel = (risk as any).riskLevel || (risk as any).level;
                    highlightElement.className = `risk-item ${this.getRiskLevelClass(riskLevel)}`;
                    if (risk.type && risk.type.toLowerCase().includes('table')) {
                        highlightElement.classList.add('risk-table');
                    }
                    highlightElement.dataset.riskId = risk.objectId;
                    
                    // 新增：检查并应用 active 状态
                    if (this.activeRiskId === risk.objectId) {
                        highlightElement.classList.add('active');
                    }
                    
                    // 从对角坐标计算位置和尺寸 [x1, y1, x2, y2]
                    const [x1, y1, x2, y2] = point;
                    
                    // 应用缩放比例
                    const scaledX1 = x1 * scale;
                    const scaledY1 = y1 * scale;
                    const scaledX2 = x2 * scale;
                    const scaledY2 = y2 * scale;
                    
                    const width = Math.abs(scaledX2 - scaledX1);
                    const height = Math.abs(scaledY2 - scaledY1);
                    const left = Math.min(scaledX1, scaledX2);
                    const top = Math.min(scaledY1, scaledY2);
                    
                    // 调试输出尺寸信息（仅在开发环境）
                    if (process.env.NODE_ENV !== 'production') {
                        console.log(`Risk ${risk.objectId} 矩形原始尺寸: ${Math.abs(x2-x1)}x${Math.abs(y2-y1)}`);
                        console.log(`Risk ${risk.objectId} 缩放比例: ${scale}`);
                        console.log(`Risk ${risk.objectId} 缩放后尺寸: ${width}x${height}`);
                    }
                    
                    // 设置位置和尺寸样式
                    highlightElement.style.position = 'absolute';
                    highlightElement.style.left = `${left}px`;
                    highlightElement.style.top = `${top}px`;
                    highlightElement.style.width = `${width}px`;
                    highlightElement.style.height = `${height}px`;
                    
                    // 添加点击事件 - 仅高亮，不滚动
                    //许总需求要求不需要点击
                    // highlightElement.addEventListener('click', () => {
                    //     // 直接点击高亮元素时，只进行高亮处理，不滚动
                    //     const pageElement = this.pageElements[pageNumber - 1];
                    //     if (pageElement) {
                    //         this.highlightRisk(pageElement, risk.objectId, false);
                    //
                    //         // 发送风险项被点击的自定义事件
                    //         const riskClickedEvent = new CustomEvent('riskClicked', {
                    //             detail: {
                    //                 riskId: risk.objectId,
                    //                 risk: risk
                    //             },
                    //             bubbles: true,
                    //             cancelable: true
                    //         });
                    //         highlightElement.dispatchEvent(riskClickedEvent);
                    //     }
                    // });
                    
                    // 将高亮元素添加到文档片段，而不是直接添加到风险层
                    fragment.appendChild(highlightElement);
                });
            });
        });
        
        // 如果有风险项，则添加风险层到页面容器
        if (fragment.childNodes.length > 0) {
            // 检查是否已有风险层，如果有则移除
            const existingRiskLayer = pageContainer.querySelector('.pdf-risk-layer');
            if (existingRiskLayer) {
                pageContainer.removeChild(existingRiskLayer);
            }
            
            // 一次性将所有高亮元素添加到风险层
            riskLayer.appendChild(fragment);
            
            // 添加新的风险层
            pageContainer.appendChild(riskLayer);
        }
    }

    /**
     * 根据风险等级获取对应的CSS类名
     * @param level 风险等级
     * @returns 对应的CSS类名
     */
    private getRiskLevelClass(level: string | undefined): string {
        // 添加空值检查，如果level是undefined，则返回默认值
        if (!level) {
            return 'risk-default';
        }
        
        switch(level.toLowerCase()) {
            case '0':
                //提取要素
                return 'risk-extract';
            case 'high':
            case '1':
                return 'risk-high';
            case 'medium':
            case '2':
                return 'risk-medium';
            case 'low':
            case '3':
                return 'risk-low';
            default:
                return 'risk-default';
        }
    }

    /**
     * 高亮显示风险项
     * @param pageElement 页面元素
     * @param riskId 风险项ID
     * @param shouldScroll 是否滚动到风险项
     */
    private highlightRisk(pageElement: HTMLElement, riskId: string, shouldScroll: boolean = true): void {
        // 移除所有高亮
        document.querySelectorAll('.risk-item.active').forEach(item => {
            item.classList.remove('active');
        });
        this.activeRiskId = null; // 新增：清除之前的激活状态

        const pageContainer = pageElement.parentElement;
        if (!pageContainer) {
            console.warn(`Page container not found for pageElement while trying to highlight riskId ${riskId}`);
            return;
        }
        
        // 找到要高亮的风险项 (在 pageContainer 中查找)
        const riskElements = pageContainer.querySelectorAll(`.risk-item[data-risk-id="${riskId}"]`);
        
        if (riskElements.length === 0) {
            console.warn(`页面上未找到风险项 ${riskId} 的元素`);
            return; // 确保在未找到元素时 activeRiskId 保持 null
        }
        
        riskElements.forEach(element => {
            element.classList.add('active');
        });
        this.activeRiskId = riskId; // 新增：设置新的激活状态
        
        // 如果需要滚动，则滚动到第一个风险项元素
        if (shouldScroll && riskElements.length > 0) {
            const firstRiskElement = riskElements[0] as HTMLElement;
            const riskRect = firstRiskElement.getBoundingClientRect();
            const containerRect = this.container.getBoundingClientRect();
            
            // 判断风险项是否在容器的可视区域内
            const isVisible = (
                // 风险项底部是否在容器顶部以下
                riskRect.bottom > containerRect.top &&
                // 风险项顶部是否在容器底部以上
                riskRect.top < containerRect.bottom &&
                // 确保至少有一部分风险项在可视区域内
                (riskRect.height > 0)
            );
            
            // 记录详细的位置信息用于调试
            console.log(`风险项位置: top=${riskRect.top.toFixed(1)}, bottom=${riskRect.bottom.toFixed(1)}, 容器位置: top=${containerRect.top.toFixed(1)}, bottom=${containerRect.bottom.toFixed(1)}`);
            
            if (!isVisible) {
                console.log(`风险项不在可视区域，执行滚动`);
                
                // 计算滚动位置，使风险项在容器中居中
                const centerPosition = this.container.scrollTop + (riskRect.top - containerRect.top) - this.container.clientHeight / 2 + riskRect.height / 2;
                
                // 平滑滚动到风险项
                this.container.scrollTo({
                    top: centerPosition,
                    behavior: 'smooth'
                });
            } else {
                // 检查是否风险项几乎完全可见
                const visibleHeight = Math.min(containerRect.bottom, riskRect.bottom) - Math.max(containerRect.top, riskRect.top);
                const visibilityRatio = visibleHeight / riskRect.height;
                
                // 只有当可见比例低于阈值时才滚动
                if (visibilityRatio < 0.8) {  // 要求80%可见
                    console.log(`风险项部分可见(${(visibilityRatio * 100).toFixed(1)}%)，需要调整位置`);
                    
                    // 计算滚动位置，使风险项在容器中居中
                    const centerPosition = this.container.scrollTop + (riskRect.top - containerRect.top) - this.container.clientHeight / 2 + riskRect.height / 2;
                    
                    // 平滑滚动到风险项
                    this.container.scrollTo({
                        top: centerPosition,
                        behavior: 'smooth'
                    });
                } else {
                    console.log(`风险项已充分可见(${(visibilityRatio * 100).toFixed(1)}%)，无需滚动`);
                }
            }
        }
    }

    /**
     * 检查当前可见的页面并加载
     */
    private checkVisiblePages(): void {
        // 如果未加载PDF文档或没有页面容器，则返回
        if (!this.pdfDoc || !this.container || !this.pagesContainer) return;
        
        // 获取容器的尺寸
        const visibleHeight = this.container.clientHeight;
        const containerTop = this.container.getBoundingClientRect().top;
        const pages = this.pageElements;
        
        let visiblePageFound = false;
        
        for (let i = 0; i < pages.length; i++) {
            const page = pages[i];
            const pageRect = page.getBoundingClientRect();
            const pageTop = pageRect.top - containerTop;
            const pageBottom = pageRect.bottom - containerTop;
            
            // 判断页面是否在可视区域内
            const isVisible = (pageTop < visibleHeight && pageBottom > 0);
          
            if (isVisible) {
                visiblePageFound = true;
                const pageNumber = i + 1;
                
                // 只有当页面尚未加载时，才将其加入加载队列
                if (!this.loadedPages.has(pageNumber)) {
                    this.queuePageForLoading(pageNumber);
                }
            }
        }
        
        // 只在找到可见页面时显示页面指示器
        if (visiblePageFound) {
            this.showPageIndicator();
        }
    }
    
    /**
     * 容器滚动事件处理
     */
    private onContainerScroll(): void {
        this.checkVisiblePages();
        this.showPageIndicator();
    }
    
    /**
     * 窗口大小改变事件处理
     */
    private onWindowResize(): void {
        // 如果未加载PDF文档或没有页面容器，则返回
        if (!this.pdfDoc || !this.pagesContainer) return;
        
        // 获取当前可见页面
        const currentPage = this.getCurrentPage();
        
        // 取消所有正在进行的渲染任务
        this.currentRenderTasks.forEach((task) => {
            task.cancel();
        });
        this.currentRenderTasks.clear();
        
        // 清空加载状态
        this.loadedPages.clear();
        this.pageLoadQueue = [];
        this.isLoadingPage = false;
        
        // 重新渲染当前可见页面
        // 首先重新初始化页面
        this.initializePages().then(() => {
            // 然后跳转到之前的页面
            if (currentPage > 0) {
                this.goToPage(currentPage);
            }
        });
    }
    
    /**
     * 初始化页面指示器
     */
    private initPageIndicator(): void {
        // 创建页面指示器元素
        this.pageIndicator = document.createElement('div');
        this.pageIndicator.className = 'pdf-page-indicator';
        this.pageIndicator.style.display = 'none';
        this.pageIndicator.style.zIndex = '9999'; // 确保在JS中也设置高z-index
        
        // 将页面指示器添加到容器
        this.container.appendChild(this.pageIndicator);
        
        // 添加点击事件，点击指示器时显示页面跳转输入框
        this.pageIndicator.addEventListener('click', () => {
            // 获取当前页码和总页数
            const currentPage = this.getCurrentPage();
            const totalPages = this.getTotalPages();
            
            // 创建弹出式输入框
            const inputContainer = document.createElement('div');
            inputContainer.className = 'pdf-page-input-container';
            
            // 创建表单
            const form = document.createElement('form');
            form.innerHTML = `
                <input type="number" min="1" max="${totalPages}" value="${currentPage}" required>
                <span> / ${totalPages}</span>
                <button type="submit">跳转</button>
            `;
            
            // 处理表单提交
            form.addEventListener('submit', (event) => {
                event.preventDefault();
                const input = form.querySelector('input') as HTMLInputElement;
                const pageNumber = parseInt(input.value, 10);
                
                if (pageNumber >= 1 && pageNumber <= totalPages) {
                    this.goToPage(pageNumber);
                    document.body.removeChild(inputContainer);
                }
            });
            
            inputContainer.appendChild(form);
            document.body.appendChild(inputContainer);
            
            // 聚焦输入框
            const input = form.querySelector('input') as HTMLInputElement;
            input.focus();
            input.select();
            
            // 点击外部关闭输入框
            document.addEventListener('click', function closeHandler(e) {
                if (!inputContainer.contains(e.target as Node)) {
                    if (document.body.contains(inputContainer)) {
                        document.body.removeChild(inputContainer);
                    }
                    document.removeEventListener('click', closeHandler);
                }
            });
        });
    }
    
    /**
     * 显示页面指示器
     */
    private showPageIndicator(): void {
        if (!this.pageIndicator) return;
        
        // 获取当前页码和总页数
        const currentPage = this.getCurrentPage();
        const totalPages = this.getTotalPages();
      
        // 更新页面指示器内容
        this.pageIndicator.innerHTML = `${currentPage} / ${totalPages}`;
        this.pageIndicator.style.display = 'block';
        
        // 清除之前的隐藏定时器
        if (this.hideIndicatorTimeout !== null) {
            window.clearTimeout(this.hideIndicatorTimeout);
        }
        
        // 设置新的隐藏定时器，3秒后隐藏指示器
        this.hideIndicatorTimeout = window.setTimeout(() => {
            if (this.pageIndicator) {
                this.pageIndicator.style.display = 'none';
            }
            this.hideIndicatorTimeout = null;
        }, 3000);
    }

    /**
     * A获取当前可见页码
     * @returns 当前页码
     */
    public getCurrentPage(): number {
        // 如果未加载PDF文档或没有页面容器，则返回0
        if (!this.pdfDoc || !this.container || !this.pagesContainer) return 0;
        
        const visibleHeight = this.container.clientHeight;
        const containerTop = this.container.getBoundingClientRect().top;
        
        let currentPage = 0;
        let maxVisibleRatio = 0;
        
        // 计算每个页面的可见比例，选择可见比例最大的页面
        this.pageElements.forEach((page, index) => {
            const pageRect = page.getBoundingClientRect();
            const pageTop = pageRect.top - containerTop;
            const pageBottom = pageRect.bottom - containerTop;
            const pageHeight = pageRect.height;
            
            // 计算页面在视口内的部分
            const visibleTop = Math.max(0, pageTop);
            const visibleBottom = Math.min(visibleHeight, pageBottom);
            const visiblePageHeight = Math.max(0, visibleBottom - visibleTop);
            
            // 计算可见比例
            const visibleRatio = visiblePageHeight / pageHeight;
            
            if (visibleRatio > maxVisibleRatio) {
                maxVisibleRatio = visibleRatio;
                currentPage = index + 1;
            }
        });
        
        return currentPage;
    }
    
    /**
     * 获取PDF文档总页数
     * @returns 总页数
     */
    public getTotalPages(): number {
        return this.pdfDoc ? this.pdfDoc.numPages : 0;
    }
    
    /**
     * 加载新的PDF文档
     * @param pdfPath PDF文件路径
     */
    public async loadDocument(pdfPath: string): Promise<void> {
        // 清除当前的PDF文档
        if (this.pdfDoc) {
            // 清除所有渲染任务
            this.currentRenderTasks.forEach((task) => {
                task.cancel();
            });
            this.currentRenderTasks.clear();
            
            // 清除加载状态
            this.loadedPages.clear();
            this.pageLoadQueue = [];
            this.isLoadingPage = false;
            
            // 清除风险项
            this.clearRisks();
            
            // 清除页面元素
            this.pageElements = [];
            if (this.pagesContainer) {
                this.pagesContainer.innerHTML = '';
            }
        }
        
        // 加载新的PDF文档
        await this.loadPdf(pdfPath);
    }

    /**
     * 设置风险项数据
     * @param risks 风险项数组
     * @returns 当所有风险项渲染完成时解析的Promise
     */
    public async setRisks(risks: PdfRisk[]): Promise<void> {
        // 确保risks是数组
        if (!Array.isArray(risks)) {
            console.error('setRisks: risks参数不是数组');
            return;
        }
        
        this.risks = [...risks];
        
        // 创建一个数组来收集所有页面风险项渲染的Promise
        const renderPromises: Promise<void>[] = [];
        
        // 重新渲染已加载页面上的风险项
        this.loadedPages.forEach((pageNumber) => {
            const pageElement = this.pageElements[pageNumber - 1];
            if (!pageElement) return;
            
            // 创建一个Promise来处理这个页面上的风险项渲染
            const renderPromise = (async () => {
                // 如果页面已经渲染了风险项，移除旧的风险项
                const existingRiskLayer = pageElement.querySelector('.pdf-risk-layer');
                if (existingRiskLayer) {
                    pageElement.removeChild(existingRiskLayer);
                }
                
                try {
                    // 重新获取页面以计算正确的视口信息
                    if (this.pdfDoc) {
                        const page = await this.pdfDoc.getPage(pageNumber);
                        
                        // 获取canvas元素，从中提取缩放信息
                        const canvas = pageElement.querySelector('canvas');
                        if (canvas) {
                            const viewport = page.getViewport({ scale: 1.0 });
                            const scale = canvas.width / viewport.width;
                            
                            // 创建视口信息对象
                            const viewportInfo = {
                                width: canvas.width,
                                height: canvas.height,
                                originalWidth: viewport.width,
                                originalHeight: viewport.height,
                                scale: scale
                            };
                            
                            // 渲染风险项
                            this.renderRisks(pageNumber, viewportInfo);
                        }
                    }
                } catch (error) {
                    console.error(`为页面 ${pageNumber} 重新渲染风险项时出错:`, error);
                }
            })();
            
            // 将这个页面的渲染Promise添加到数组
            renderPromises.push(renderPromise);
        });
        
        // 等待所有页面的风险项渲染完成
        await Promise.all(renderPromises);

        const visibleCount = this.getVisibleRisksCount();
        const filterInfo = this.visibleRiskLevels ? ` (筛选显示 ${visibleCount} 个)` : '';
        console.log(`所有风险项(${this.risks.length})渲染完成${filterInfo}`);
    }

    /**
     * 获取当前设置的风险项
     * @returns 风险项数组
     */
    public getRisks(): PdfRisk[] {
        return [...this.risks];
    }

    /**
     * 清除所有风险项
     */
    public clearRisks(): void {
        this.risks = [];
        this.visibleRiskLevels = null; // 重置风险等级筛选

        // 移除所有页面上的风险层
        this.pageElements.forEach(pageElement => {
            const riskLayer = pageElement.querySelector('.pdf-risk-layer');
            if (riskLayer) {
                pageElement.removeChild(riskLayer);
            }
        });
    }

    /**
     * 根据风险等级显示对应的风险项
     * @param levels 风险等级数组，如 ['0', '1', '2', '3']，其中 '0'-提取要素，'1'-高风险，'2'-中风险，'3'-低风险
     */
    public async showRisksByLevels(levels: string[]): Promise<void> {
        // 验证输入参数
        if (!Array.isArray(levels)) {
            console.error('showRisksByLevels: levels参数必须是数组');
            return;
        }

        // 设置可见风险等级
        this.visibleRiskLevels = [...levels];

        console.log(`设置显示风险等级: ${levels.join(', ')}`);

        // 重新渲染所有已加载页面的风险项
        await this.reRenderAllRisks();
    }

    /**
     * 显示所有风险项（清除等级筛选）
     */
    public async showAllRisks(): Promise<void> {
        this.visibleRiskLevels = null;
        console.log('显示所有风险等级');

        // 重新渲染所有已加载页面的风险项
        await this.reRenderAllRisks();
    }

    /**
     * 获取当前可见的风险等级
     * @returns 当前可见的风险等级数组，null表示显示所有
     */
    public getVisibleRiskLevels(): string[] | null {
        return this.visibleRiskLevels ? [...this.visibleRiskLevels] : null;
    }

    /**
     * 重新渲染所有已加载页面的风险项
     * @private
     */
    private async reRenderAllRisks(): Promise<void> {
        // 如果没有风险项数据，直接返回
        if (!this.risks || this.risks.length === 0) {
            return;
        }

        // 创建一个数组来收集所有页面风险项渲染的Promise
        const renderPromises: Promise<void>[] = [];

        // 重新渲染已加载页面上的风险项
        this.loadedPages.forEach((pageNumber) => {
            const pageElement = this.pageElements[pageNumber - 1];
            if (!pageElement) return;

            // 创建一个Promise来处理这个页面上的风险项渲染
            const renderPromise = (async () => {
                // 移除旧的风险项层（可能存在多个）
                const pageContainer = pageElement.parentElement;
                if (pageContainer) {
                    const existingRiskLayers = pageContainer.querySelectorAll('.pdf-risk-layer');
                    existingRiskLayers.forEach(layer => {
                        pageContainer.removeChild(layer);
                    });

                }

                try {
                    // 重新获取页面以计算正确的视口信息
                    if (this.pdfDoc) {
                        const page = await this.pdfDoc.getPage(pageNumber);

                        // 获取canvas元素，从中提取缩放信息
                        const canvas = pageElement.querySelector('canvas');
                        if (canvas) {
                            const viewport = page.getViewport({ scale: 1.0 });
                            const scale = canvas.width / viewport.width;

                            // 创建视口信息对象
                            const viewportInfo = {
                                width: canvas.width,
                                height: canvas.height,
                                originalWidth: viewport.width,
                                originalHeight: viewport.height,
                                scale: scale
                            };

                            // 渲染风险项（会自动应用等级筛选）
                            this.renderRisks(pageNumber, viewportInfo);
                        }
                    }
                } catch (error) {
                    console.error(`为页面 ${pageNumber} 重新渲染风险项时出错:`, error);
                }
            })();

            // 将这个页面的渲染Promise添加到数组
            renderPromises.push(renderPromise);
        });

        // 等待所有页面的风险项渲染完成
        await Promise.all(renderPromises);

        const visibleCount = this.getVisibleRisksCount();
        console.log(`风险项筛选渲染完成，当前显示 ${visibleCount} 个风险项`);
    }

    /**
     * 获取当前可见的风险项数量
     * @private
     */
    private getVisibleRisksCount(): number {
        if (!this.risks || this.risks.length === 0) {
            return 0;
        }

        if (this.visibleRiskLevels === null) {
            return this.risks.length;
        }

        return this.risks.filter(risk => {
            const riskLevel = risk.riskLevel || (risk as any).level;
            return this.visibleRiskLevels!.includes(riskLevel);
        }).length;
    }
    
    /**
     * 跳转到指定页面并调整滚动位置
     * @param pageNumber 页码
     */
    private async jumpToPageWithAdjustedHeight(pageNumber: number, scrollBehavior: ScrollBehavior = 'smooth'): Promise<void> {
        if (!this.pdfDoc || !this.container || !this.pagesContainer) return;
        
        // 确保页面索引有效
        if (pageNumber < 1 || pageNumber > this.pdfDoc.numPages) {
            console.warn(`页码 ${pageNumber} 超出范围`);
            return;
        }
        
        // 获取页面元素
        const pageElement = this.pageElements[pageNumber - 1];
        if (!pageElement) return;
        
        // 获取页面位置信息
        const containerRect = this.container.getBoundingClientRect();
        const pageRect = pageElement.getBoundingClientRect();
        
        // 计算页面相对于容器的位置
        const relativeTop = pageRect.top - containerRect.top;
        
        // 计算滚动位置，使页面顶部与容器顶部对齐
        const scrollTop = this.container.scrollTop + relativeTop;
        
        // 平滑滚动到页面
        this.container.scrollTo({
            top: scrollTop,
            behavior: scrollBehavior
        });
        
        // 显示页面指示器
        this.showPageIndicator();
    }
    
    /**
     * 确保页面已渲染
     * @param pageNumber 页码
     */
    private async ensurePageRendered(pageNumber: number): Promise<void> {
        // 如果页面已加载，则直接返回
        if (this.loadedPages.has(pageNumber)) return;
        
        // 否则加载页面
        return new Promise<void>((resolve, reject) => {
            // 将页面加入加载队列
            this.queuePageForLoading(pageNumber);
            
            // 设置轮询检查页面是否已加载
            const checkInterval = 100; // 检查间隔(毫秒)
            const maxWaitTime = 10000; // 最大等待时间(毫秒)
            let elapsedTime = 0;
            
            const checkLoaded = () => {
                if (this.loadedPages.has(pageNumber)) {
                    // 页面已加载
                    resolve();
                    return;
                }
                
                // 超过最大等待时间
                if (elapsedTime >= maxWaitTime) {
                    reject(new Error(`等待页面 ${pageNumber} 渲染超时`));
                    return;
                }
                
                // 继续等待
                elapsedTime += checkInterval;
                setTimeout(checkLoaded, checkInterval);
            };
            
            // 开始检查
            checkLoaded();
        });
    }
    
    /**
     * 清理未使用的页面，释放内存
     * @param currentPage 当前页码
     * @param keepRange 保留当前页面前后的页数范围
     */
    private cleanUpUnusedPages(currentPage: number, keepRange: number = 10): void {
        // 如果未加载PDF文档或页面太少，则不进行清理
        if (!this.pdfDoc || this.pdfDoc.numPages <= keepRange * 2) return;
        
        // 计算要保留的页面范围
        const keepStart = Math.max(1, currentPage - keepRange);
        const keepEnd = Math.min(this.pdfDoc.numPages, currentPage + keepRange);
        
        // 遍历所有已加载的页面，清理范围外的页面
        const pagesToRemove: number[] = [];
        
        this.loadedPages.forEach(pageNumber => {
            if (pageNumber < keepStart || pageNumber > keepEnd) {
                pagesToRemove.push(pageNumber);
            }
        });
        
        // 清理页面
        pagesToRemove.forEach(pageNumber => {
            // 从已加载页面集合中移除
            this.loadedPages.delete(pageNumber);
            
            // 获取页面元素
            const pageElement = this.pageElements[pageNumber - 1];
            if (!pageElement) return;
            
            // 取消正在进行的渲染任务
            const renderTask = this.currentRenderTasks.get(pageNumber);
            if (renderTask) {
                renderTask.cancel();
                this.currentRenderTasks.delete(pageNumber);
            }
            
            // 清空页面内容，保留占位符
            const pageHeight = pageElement.offsetHeight;
            
            // 清空页面内容
            pageElement.innerHTML = '';
            pageElement.style.height = `${pageHeight}px`;
            
            // 调试日志
            if (process.env.NODE_ENV !== 'production') {
                console.log(`清理了页面 ${pageNumber}`);
            }
        });
        
        // 如果清理了页面，记录日志
        if (pagesToRemove.length > 0 && process.env.NODE_ENV !== 'production') {
            console.log(`清理了 ${pagesToRemove.length} 个页面`);
            
            // 在某些浏览器中，可以尝试建议执行垃圾回收
            if (typeof window !== 'undefined' && (window as any).gc) {
                (window as any).gc();
            }
        }
    }
    
    /**
     * 计算估计的页面高度
     * 用于页面跳转时的初始估计
     * @returns 估计的页面高度(像素)
     */
    private calculateEstimatedPageHeight(): number {
        if (this.pageElements.length === 0) return 1000; // 默认值
        
        // 计算已加载页面的平均高度
        let totalHeight = 0;
        let loadedCount = 0;
        
        this.loadedPages.forEach(pageNumber => {
            const pageElement = this.pageElements[pageNumber - 1];
            if (pageElement) {
                totalHeight += pageElement.offsetHeight;
                loadedCount++;
            }
        });
        
        // 如果有加载的页面，返回平均高度
        if (loadedCount > 0) {
            return totalHeight / loadedCount;
        }
        
        // 否则返回第一个页面的高度或默认值
        const firstPage = this.pageElements[0];
        return firstPage ? firstPage.offsetHeight : 1000;
    }
    
    /**
     * 跳转到指定页面
     * @param pageNumber 页码
     */
    public async goToPage(pageNumber: number, scrollBehavior: ScrollBehavior = 'smooth'): Promise<void> {
        if (!this.pdfDoc || !this.container || !this.pagesContainer) return;
        
        // 确保页码在有效范围内
        const validPageNumber = Math.max(1, Math.min(pageNumber, this.pdfDoc.numPages));
        
        try {
            // 确保目标页面已渲染
            await this.ensurePageRendered(validPageNumber);
            
            // 跳转到页面
            await this.jumpToPageWithAdjustedHeight(validPageNumber, scrollBehavior);
            
            // 清理未使用的页面
            this.cleanUpUnusedPages(validPageNumber);
        } catch (error) {
            console.error(`跳转到页面 ${pageNumber} 时出错:`, error);
            
            // 退回到没有预渲染的跳转方法
            // 计算估计的目标位置
            const estimatedPageHeight = this.calculateEstimatedPageHeight();
            const estimatedPosition = (validPageNumber - 1) * estimatedPageHeight;
            
            // 直接跳转
            this.container.scrollTo({
                top: estimatedPosition,
                behavior: 'smooth'
            });
            
            // 显示页面指示器
            this.showPageIndicator();
        }
    }

    /**
     * 清理资源
     * 取消所有未完成的渲染任务，清空缓存
     */
    private cleanup(): void {
      // 取消所有渲染任务
      this.currentRenderTasks.forEach(task => {
        if (task && task.cancel) {
          task.cancel();
        }
      });
      this.currentRenderTasks.clear();
      
      // 清空页面加载队列
      this.pageLoadQueue = [];
      this.isLoadingPage = false;
      
      // 停止内存监控
      this.stopMemoryMonitoring();
      
      // 清除计时器
      if (this.hideIndicatorTimeout !== null) {
        window.clearTimeout(this.hideIndicatorTimeout);
        this.hideIndicatorTimeout = null;
      }
    }
    
    /**
     * 销毁实例
     * 清理所有资源并移除事件监听器
     */
    public dispose(): void {
        // 清理资源
        this.cleanup();
        
        // 移除事件监听器
        window.removeEventListener('resize', this.onWindowResize.bind(this));
        this.container.removeEventListener('scroll', this.onContainerScroll.bind(this));
        document.removeEventListener('keydown', this.handleKeyDown.bind(this));
        
        // 断开Intersection Observer
        if (this.pagesObserver) {
            this.pagesObserver.disconnect();
            this.pagesObserver = null;
        }
        
        // 清空容器
        if (this.pagesContainer) {
            this.pagesContainer.innerHTML = '';
        }
        
        // 移除页面指示器
        if (this.pageIndicator && this.pageIndicator.parentNode) {
            this.pageIndicator.parentNode.removeChild(this.pageIndicator);
        }
        
        // 清空数据
        this.pdfDoc = null;
        this.risks = [];
        this.pageElements = [];
        this.loadedPages.clear();
    }

    /**
     * 初始化页面观察者
     * 用于监测页面可见性并触发懒加载
     */
    private initPagesObserver(): void {
        // 如果已存在观察者，先断开连接
        if (this.pagesObserver) {
            this.pagesObserver.disconnect();
            this.pagesObserver = null;
        }

        // 创建Intersection Observer监控页面可见性
        this.pagesObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // 页面可见时开始渲染
                    const pageElement = entry.target as HTMLElement;
                    const pageNumberStr = pageElement.dataset.pageNumber;
                    if (pageNumberStr) {
                        const pageNumber = parseInt(pageNumberStr, 10);
                        if (pageNumber > 0 && !this.loadedPages.has(pageNumber)) {
                            this.queuePageForLoading(pageNumber);
                        }
                    }
                }
            });
        }, {
            root: this.container,
            // 增加预加载范围，提前开始渲染
            rootMargin: '200px 0px',
            // 页面只要有10%进入视口就开始渲染
            threshold: 0.1
        });

        // 为所有页面元素添加观察者
        this.pageElements.forEach(element => {
            this.pagesObserver?.observe(element);
        });
    }

    /**
     * 将页面加入加载队列
     * @param pageNumber 要加载的页码
     */
    private queuePageForLoading(pageNumber: number): void {
        // 如果页面已经加载，则跳过
        if (this.loadedPages.has(pageNumber)) {
            return;
        }
        
        // 直接渲染页面
        this.renderPage(pageNumber);
        
        // 标记页面为已加载
        this.loadedPages.add(pageNumber);
    }

    /**
     * 处理页面加载队列
     */
    // @ts-ignore - 该方法在原始代码中用于处理页面加载队列
    private async processPageLoadQueue(): Promise<void> {
        // 简化实现，实际不执行任何操作
        // 该方法在原始实现中会处理页面加载队列
    }

    /**
     * 处理风险菜单点击事件
     * 跳转到对应的风险项位置并高亮显示
     * @param riskId 风险项ID
     */
    public handleRiskMenuClick(riskId: string): void {
        // 如果未加载PDF文档或没有风险项数据，则返回
        if (!this.pdfDoc || !this.risks || this.risks.length === 0) {
            console.warn('尚未加载PDF文档或风险项数据');
            return;
        }

        // 查找风险项
        const risk = this.risks.find(r => r.objectId === riskId);
        if (!risk) {
            console.warn(`未找到ID为 ${riskId} 的风险项`);
            return;
        }

        // 获取风险项所在的第一个页面的索引
        const firstAnchor = risk.anchors[0];
        if (!firstAnchor) {
            console.warn(`风险项 ${riskId} 没有锚点数据`);
            return;
        }

        // 获取页码 (页面索引直接对应页码)
        const pageNumber = firstAnchor.pageIndex;

        // 检查目标页面是否已经在视图中并充分可见
        const isPageSufficientlyVisible = this.isPageSufficientlyVisible(pageNumber);
        console.log(`检查页面 ${pageNumber} 是否充分可见:`, isPageSufficientlyVisible);

        if (isPageSufficientlyVisible) {
            // 如果页面已充分可见，直接高亮风险项（添加短延时确保页面稳定）
            console.log(`页面 ${pageNumber} 已充分可见，直接高亮风险项 ${riskId}`);
            
            // 使用短延时确保任何进行中的滚动操作已完成
            setTimeout(() => {
                const pageElement = this.pageElements[pageNumber - 1];
                if (pageElement) {
                    this.highlightRisk(pageElement, riskId, true);
                    
                    // 发送风险项被选中的自定义事件
                    const riskSelectedEvent = new CustomEvent('riskSelected', {
                        detail: {
                            riskId: riskId,
                            risk: risk
                        },
                        bubbles: true,
                        cancelable: true
                    });
                    
                    // 在文档上触发事件，以便外部监听器可以捕获
                    document.dispatchEvent(riskSelectedEvent);
                }
            }, 50);
        } else {
            // 如果页面不可见或不充分可见，先快速跳转到该页面，然后高亮风险项
            console.log(`页面 ${pageNumber} 不在视图中，需要先跳转再高亮`);
            
            // 使用'auto'滚动行为快速跳转到目标页面
            this.goToPage(pageNumber, 'auto').then(() => {
                // 页面加载完成后，等待一段时间确保页面已稳定，然后高亮风险项
                setTimeout(() => {
                    const pageElement = this.pageElements[pageNumber - 1];
                    if (pageElement) {
                        this.highlightRisk(pageElement, riskId, true);
                        
                        // 发送风险项被选中的自定义事件
                        const riskSelectedEvent = new CustomEvent('riskSelected', {
                            detail: {
                                riskId: riskId,
                                risk: risk
                            },
                            bubbles: true,
                            cancelable: true
                        });
                        
                        // 在文档上触发事件，以便外部监听器可以捕获
                        document.dispatchEvent(riskSelectedEvent);
                    }
                }, 100);
            }).catch(error => {
                console.error(`跳转到风险项 ${riskId} 失败:`, error);
            });
        }
    }

    /**
     * 检查页面是否充分可见
     * @param pageNumber 页码
     * @returns 页面是否充分可见
     */
    private isPageSufficientlyVisible(pageNumber: number): boolean {
        if (!this.container || pageNumber < 1 || pageNumber > this.pageElements.length) {
            return false;
        }

        // 获取页面元素
        const pageElement = this.pageElements[pageNumber - 1];
        if (!pageElement) return false;

        // 获取页面和容器的位置信息
        const containerRect = this.container.getBoundingClientRect();
        const pageRect = pageElement.getBoundingClientRect();

        // 计算页面在视口中的可见部分
        const visibleTop = Math.max(pageRect.top, containerRect.top);
        const visibleBottom = Math.min(pageRect.bottom, containerRect.bottom);
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);

        // 计算页面的可见比例
        const visibilityRatio = visibleHeight / pageRect.height;
        
        // 记录详细信息用于调试
        console.log(`页面 ${pageNumber} 可见性: ${(visibilityRatio * 100).toFixed(1)}%, 位置: top=${pageRect.top.toFixed(1)}, bottom=${pageRect.bottom.toFixed(1)}, 容器: top=${containerRect.top.toFixed(1)}, bottom=${containerRect.bottom.toFixed(1)}`);

        // 页面至少有10%在视口中即视为充分可见
        return visibilityRatio >= 0.1;
    }
}