/**
 * 浏览器检测工具类
 * 用于检测浏览器类型和版本，特别是Chrome版本检测
 */
export class BrowserDetector {
    /**
     * 检测当前浏览器信息
     * @returns 浏览器信息对象
     */
    static detectBrowser(): { name: string; version: number | null; isChrome: boolean } {
        const userAgent = navigator.userAgent;
        
        // 检测Chrome浏览器
        if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
            const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
            const version = chromeMatch ? parseInt(chromeMatch[1], 10) : null;
            
            return {
                name: 'Chrome',
                version,
                isChrome: true
            };
        }
        
        // 检测其他浏览器（Edge, Firefox, Safari等）
        if (userAgent.includes('Edg')) {
            const edgeMatch = userAgent.match(/Edg\/(\d+)/);
            const version = edgeMatch ? parseInt(edgeMatch[1], 10) : null;
            return { name: 'Edge', version, isChrome: false };
        }
        
        if (userAgent.includes('Firefox')) {
            const firefoxMatch = userAgent.match(/Firefox\/(\d+)/);
            const version = firefoxMatch ? parseInt(firefoxMatch[1], 10) : null;
            return { name: 'Firefox', version, isChrome: false };
        }
        
        if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
            const safariMatch = userAgent.match(/Version\/(\d+)/);
            const version = safariMatch ? parseInt(safariMatch[1], 10) : null;
            return { name: 'Safari', version, isChrome: false };
        }
        
        return { name: 'Unknown', version: null, isChrome: false };
    }
    
    /**
     * 检查浏览器版本是否需要更新
     * @returns 是否需要更新
     */
    static needsBrowserUpdate(): boolean {
        const browser = this.detectBrowser();

        // 如果无法获取版本号，建议更新
        if (browser.version === null) {
            return true;
        }

        // 根据浏览器类型检查最低版本要求
        switch (browser.name) {
            case 'Chrome':
                return browser.version < 110;
            case 'Firefox':
                return browser.version < 102; // Firefox ESR 102+
            case 'Safari':
                return browser.version < 16; // Safari 16.4+ (使用主版本号16)
            case 'Edge':
                return browser.version < 110; // Edge基于Chromium，要求与Chrome相同
            default:
                return true; // 未知浏览器建议更新
        }
    }
    
    /**
     * 获取浏览器更新建议信息
     * @returns 更新建议信息
     */
    static getBrowserUpdateInfo(): { needsUpdate: boolean; currentVersion: number | null; browserName: string; message: string } {
        const browser = this.detectBrowser();
        const needsUpdate = this.needsBrowserUpdate();
        const currentVersion = browser.version;

        if (needsUpdate) {
            const versionText = currentVersion ? `${currentVersion}` : '未知版本';
            let requiredVersion = '';
            let downloadLink = '';

            switch (browser.name) {
                case 'Chrome':
                    requiredVersion = '110+';
                    downloadLink = 'https://www.google.com/chrome/';
                    break;
                case 'Firefox':
                    requiredVersion = 'ESR 102+';
                    downloadLink = 'https://www.mozilla.org/firefox/';
                    break;
                case 'Safari':
                    requiredVersion = '16.4+';
                    downloadLink = 'https://support.apple.com/safari/';
                    break;
                case 'Edge':
                    requiredVersion = '110+';
                    downloadLink = 'https://www.microsoft.com/edge/';
                    break;
                default:
                    requiredVersion = '最新版本';
                    downloadLink = 'https://www.google.com/chrome/';
                    break;
            }

            return {
                needsUpdate: true,
                currentVersion,
                browserName: browser.name,
                message: `您当前使用的${browser.name}版本（${versionText}）过低，建议更新到${requiredVersion}以获得最佳体验。`
            };
        }

        return {
            needsUpdate: false,
            currentVersion,
            browserName: browser.name,
            message: '浏览器版本符合要求'
        };
    }

    /**
     * 获取浏览器下载链接
     * @param browserName 浏览器名称
     * @returns 下载链接
     */
    static getBrowserDownloadLink(browserName: string): string {
        switch (browserName) {
            case 'Chrome':
                return 'https://www.google.com/chrome/';
            case 'Firefox':
                return 'https://www.mozilla.org/firefox/';
            case 'Safari':
                return 'https://support.apple.com/safari/';
            case 'Edge':
                return 'https://www.microsoft.com/edge/';
            default:
                return 'https://www.google.com/chrome/'; // 默认推荐Chrome
        }
    }
}
