/**
 * 浏览器警告组件
 * 用于显示不可关闭的浏览器更新提醒
 */
export class BrowserWarning {
    private warningElement: HTMLElement | null = null;
    
    /**
     * 显示浏览器更新警告
     * @param message 警告消息
     * @param browserName 浏览器名称
     * @param currentVersion 当前浏览器版本
     */
    showWarning(message: string, browserName: string = 'Chrome', currentVersion: number | null = null): void {
        // 如果警告已经存在，不重复创建
        if (this.warningElement && document.body.contains(this.warningElement)) {
            return;
        }
        
        // 创建警告容器
        this.warningElement = document.createElement('div');
        this.warningElement.className = 'browser-warning';
        this.warningElement.setAttribute('role', 'alert');
        this.warningElement.setAttribute('aria-live', 'polite');
        
        // 创建警告内容
        const versionInfo = currentVersion ? ` (当前版本: ${currentVersion})` : '';

        // 根据浏览器类型获取下载链接和按钮文本
        const getBrowserInfo = (browserName: string) => {
            switch (browserName) {
                case 'Chrome':
                    return {
                        link: 'https://www.google.com/chrome/',
                        text: '立即更新Chrome浏览器'
                    };
                case 'Firefox':
                    return {
                        link: 'https://www.mozilla.org/firefox/',
                        text: '立即更新Firefox浏览器'
                    };
                case 'Safari':
                    return {
                        link: 'https://support.apple.com/safari/',
                        text: '查看Safari更新指南'
                    };
                case 'Edge':
                    return {
                        link: 'https://www.microsoft.com/edge/',
                        text: '立即更新Edge浏览器'
                    };
                default:
                    return {
                        link: 'https://www.google.com/chrome/',
                        text: '推荐使用Chrome浏览器'
                    };
            }
        };

        const browserInfo = getBrowserInfo(browserName);

        this.warningElement.innerHTML = `
            <div class="browser-warning-content">
                <div class="browser-warning-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="currentColor"/>
                        <path d="M12 7C14.76 7 17 9.24 17 12S14.76 17 12 17 7 14.76 7 12 9.24 7 12 7ZM12 5C8.13 5 5 8.13 5 12S8.13 19 12 19 19 15.87 19 12 15.87 5 12 5Z" fill="currentColor"/>
                    </svg>
                </div>
                <div class="browser-warning-text">
                    <div class="browser-warning-title">浏览器版本过低</div>
                    <div class="browser-warning-message">${message}${versionInfo}</div>
                    <div class="browser-warning-action">
                        <a href="${browserInfo.link}" target="_blank" rel="noopener noreferrer" class="browser-warning-link">
                            ${browserInfo.text}
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        // 将警告添加到页面顶部
        document.body.insertBefore(this.warningElement, document.body.firstChild);
        
        // 添加样式（如果还没有添加）
        this.addStyles();
        
        // 确保警告在最顶层
        this.ensureTopPosition();
    }
    
    /**
     * 隐藏警告（虽然设计为不可关闭，但保留此方法以备将来需要）
     */
    hideWarning(): void {
        if (this.warningElement && document.body.contains(this.warningElement)) {
            document.body.removeChild(this.warningElement);
            this.warningElement = null;
        }
    }
    
    /**
     * 检查警告是否正在显示
     */
    isWarningVisible(): boolean {
        return this.warningElement !== null && document.body.contains(this.warningElement);
    }
    
    /**
     * 确保警告始终在页面顶部
     */
    private ensureTopPosition(): void {
        if (this.warningElement) {
            // 调整页面内容的上边距，为警告腾出空间
            const mainContainer = document.querySelector('.main-container') as HTMLElement;
            if (mainContainer) {
                mainContainer.style.marginTop = '60px'; // 为警告横幅预留空间
            }
        }
    }
    
    /**
     * 添加警告样式
     */
    private addStyles(): void {
        // 检查是否已经添加了样式
        if (document.getElementById('browser-warning-styles')) {
            return;
        }
        
        const style = document.createElement('style');
        style.id = 'browser-warning-styles';
        style.textContent = `
            .browser-warning {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: linear-gradient(135deg, #ff9500, #e67e22);
                color: white;
                padding: 12px 20px;
                z-index: 10000;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                border-bottom: 3px solid rgba(255, 255, 255, 0.3);
            }
            
            .browser-warning-content {
                display: flex;
                align-items: center;
                max-width: 1200px;
                margin: 0 auto;
                gap: 12px;
            }
            
            .browser-warning-icon {
                flex-shrink: 0;
                width: 24px;
                height: 24px;
                opacity: 0.9;
            }
            
            .browser-warning-text {
                flex: 1;
                min-width: 0;
            }
            
            .browser-warning-title {
                font-weight: 600;
                font-size: 16px;
                margin-bottom: 4px;
            }
            
            .browser-warning-message {
                font-size: 14px;
                opacity: 0.95;
                line-height: 1.4;
                margin-bottom: 8px;
            }
            
            .browser-warning-action {
                margin-top: 8px;
            }
            
            .browser-warning-link {
                display: inline-block;
                background: rgba(255, 255, 255, 0.2);
                color: white;
                text-decoration: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                border: 1px solid rgba(255, 255, 255, 0.3);
                transition: all 0.2s ease;
            }
            
            .browser-warning-link:hover {
                background: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.5);
                transform: translateY(-1px);
            }
            
            .browser-warning-link:active {
                transform: translateY(0);
            }
            
            /* 响应式设计 */
            @media (max-width: 768px) {
                .browser-warning {
                    padding: 10px 15px;
                }
                
                .browser-warning-content {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                }
                
                .browser-warning-title {
                    font-size: 15px;
                }
                
                .browser-warning-message {
                    font-size: 13px;
                }
            }
        `;
        
        document.head.appendChild(style);
    }
}
