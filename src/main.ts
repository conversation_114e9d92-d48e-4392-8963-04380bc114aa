// 首先导入 URL.parse polyfill，确保它在任何其他模块之前加载
import './polyfills/url-parse-polyfill';

import { PDFViewer } from "./ts/pdf-viewer";
import { BrowserDetector } from "./ts/browser-detector";
import { BrowserWarning } from "./ts/browser-warning";


// 检查浏览器版本并显示警告
function checkBrowserCompatibility(): boolean {
    const updateInfo = BrowserDetector.getBrowserUpdateInfo();

    if (updateInfo.needsUpdate) {
        const browserWarning = new BrowserWarning();
        browserWarning.showWarning(updateInfo.message, updateInfo.browserName, updateInfo.currentVersion);
        console.warn('浏览器版本检测:', updateInfo.message);
        return false; // 浏览器版本不符合要求
    } else {
        console.log('浏览器版本检测: 浏览器版本符合要求');
        return true; // 浏览器版本符合要求
    }
}

// 在页面加载时立即检查浏览器兼容性
const isBrowserCompatible = checkBrowserCompatibility();

// 将 PDFViewer 类暴露到全局 window 对象中
(window as any).PDFViewer = PDFViewer;

// 将浏览器兼容性状态暴露到全局，供HTML中的脚本使用
(window as any).isBrowserCompatible = isBrowserCompatible;

// 导出类以便在其他模块中使用
export { PDFViewer, BrowserDetector, BrowserWarning };
