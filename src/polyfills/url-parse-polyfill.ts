/// <reference path="./url-parse.d.ts" />

/**
 * URL.parse Polyfill
 * 
 * 为旧版浏览器提供 URL.parse 功能，解决 PDF.js 在 Chrome 121 及以下版本的兼容性问题
 * 模拟 Node.js 中 URL.parse 的基本功能
 */

// 检查是否已经存在 URL.parse 方法
if (!('parse' in URL)) {
  // 定义 URL.parse 方法
  (URL as any).parse = function(urlStr: string, baseUrl?: string | URL): URL {
    try {
      // 使用原生 URL 构造函数创建 URL 对象
      return new URL(urlStr, baseUrl);
    } catch (error) {
      console.warn('URL.parse polyfill: 创建 URL 对象失败', error);
      
      // 如果创建失败，返回一个基本的 URL 对象模拟
      // 这里我们尝试提供一个最小化实现，满足 PDF.js 的需求
      const fallbackUrl = {
        href: urlStr,
        protocol: '',
        hostname: '',
        host: '',
        pathname: urlStr,
        search: '',
        hash: '',
        toString: function() {
          return this.href;
        }
      };
      
      return fallbackUrl as unknown as URL;
    }
  };

  console.log('URL.parse polyfill 已加载，解决旧版浏览器的兼容性问题');
}

// 导出一个空函数以便能够作为模块导入
export default function setupUrlParsePolyfill() {
  // URL.parse polyfill 已在文件加载时设置
} 