



#right-menu {
  width: 300px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  border-left: 1px solid #ccc;
  background-color: #f8f8f8;
  padding: 10px;
}

/* PDF 风险项高亮样式 */
.pdf-risk-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

/* 风险项基础样式 */
.risk-default.active {
  border: 1px solid #4CA820;
  background: rgba(76, 168, 32, 0.2);
  -webkit-animation: bounce-in-data-v 1s;
  animation: bounce-in-data-v 1s;
}
.risk-high.active {
  border: 1px solid #BE0915;
  background: rgba(190,9,21,.2);
  -webkit-animation: bounce-in-data-v 1s;
  animation: bounce-in-data-v 1s;
}
.risk-medium.active {
  border: 1px solid #F98F23;
  background: rgba(249,143,35,.2);
  -webkit-animation: bounce-in-data-v 1s;
  animation: bounce-in-data-v 1s;
}
.risk-low.active {
  border: 1px solid #0E50E6;
  background: rgba(14,80,230,.2);
  -webkit-animation: bounce-in-data-v 1s;
  animation: bounce-in-data-v 1s;
}
.risk-extract.active {
  border: 1px solid #9c27b0;
  background: rgba(156, 39, 176, 0.2);
  -webkit-animation: bounce-in-data-v 1s;
  animation: bounce-in-data-v 1s;
}
.risk-item {
  position: absolute;
  border-radius: 2px;
  z-index: 10;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* 特殊风险类型样式 */
.risk-table {
  border: 1px solid #4877ff;
}

.risk-table.active {
  border: 1px solid #4877ff!important;
  box-shadow: 0 5px 16px rgba(72,119,255,.5);
}



/* 风险项激活动画 */
@keyframes bounce-in-data-v {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}



/* 确保 PDF 容器样式正确 */
#pdf-container {
  width: 100%;
  height: 100vh;
  overflow: auto;
  background-color: #f0f0f0;
}


.pdf-canvas {
  display: block;
  max-width: 100%;
}

.annotation-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

/* 添加批注高亮动画效果 */
@keyframes highlight-pulse {
  0% { box-shadow: 0 0 0 0 rgba(0, 0, 255, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(0, 0, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 0, 255, 0); }
}

.pdf-annotation {
  position: absolute;
  border: 2px solid red;
  background-color: rgba(255, 0, 0, 0.1);
  cursor: pointer;
  pointer-events: all;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}

.pdf-annotation:hover {
  background-color: rgba(255, 0, 0, 0.2);
  transform: scale(1.02);
}

.pdf-annotation.active {
  border: 2px solid blue;
  background-color: rgba(0, 0, 255, 0.1);
  z-index: 20;
  animation: highlight-pulse 1.5s ease-in-out;
}

.anno-list-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ddd;
}

.anno-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.anno-item {
  padding: 10px;
  margin-bottom: 10px;
  background-color: white;
  border-radius: 4px;
  border-left: 3px solid #ff6b6b;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.anno-item:hover {
  background-color: #f0f0f0;
  transform: translateX(2px);
}

/* 批注列表项动画 */
@keyframes list-highlight {
  0% { background-color: #e6f7ff; }
  50% { background-color: #bae7ff; }
  100% { background-color: #e6f7ff; }
}

.anno-item.active {
  border-left: 3px solid #4a6ee0;
  background-color: #e6f7ff;
  animation: list-highlight 1.5s ease-in-out;
}

.anno-id {
  font-size: 12px;
  color: #888;
  margin-bottom: 5px;
}

.anno-comment {
  font-size: 14px;
  line-height: 1.4;
}

.anno-pages {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

/* 修改页码指示器样式 */
.page-indicator {
  position: fixed; /* 改为固定定位 */
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000; /* 提高 z-index 确保在最上层 */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  pointer-events: none; /* 防止指示器阻挡点击事件 */
  transition: opacity 0.3s ease; /* 添加过渡效果 */
}

/* 添加到现有样式中 */
.page-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 300px;
  color: #666;
  font-size: 16px;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 4px;
}

/* 添加到现有样式中 */
.pdf-loading, .pdf-error {
  padding: 20px;
  margin: 20px;
  text-align: center;
}

.pdf-loading {
  color: #333;
  font-size: 18px;
}

.pdf-error {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  text-align: left;
}

.pdf-error h3 {
  color: #d9534f;
}

/* PDF页面指示器样式 */
.pdf-page-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.pdf-page-indicator:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* 页码输入框容器 */
.pdf-page-input-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  z-index: 1100;
}

.pdf-page-input-container form {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pdf-page-input-container input {
  width: 60px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  text-align: center;
}

.pdf-page-input-container button {
  padding: 8px 15px;
  background-color: #4083f7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.pdf-page-input-container button:hover {
  background-color: #3371e3;
}

/* 缩放控制样式 */
.pdf-zoom-controls {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30px;
  padding: 5px 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.zoom-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background-color: #f5f5f5;
  color: #333;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.zoom-btn:hover {
  background-color: #e0e0e0;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.zoom-reset-btn, .zoom-fit-btn {
  font-size: 12px;
  width: auto;
  padding: 0 10px;
  border-radius: 15px;
}

.zoom-reset-btn {
  background-color: #e6f7ff;
  color: #0066cc;
}

.zoom-fit-btn {
  width: auto;
  white-space: nowrap;
  padding: 0 12px;
}


/* 添加滚动条样式美化 */
#pdf-container::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

#pdf-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

#pdf-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

#pdf-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
} 