/* 导入现有样式 */
@import './style.css';

/* PDF 风险项高亮样式 */
.pdf-risk-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

/* 风险项基础样式 */
.risk-item {
  position: absolute;
  cursor: pointer;
  pointer-events: all;
  border-radius: 2px;
  z-index: 10;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* 不同风险等级的样式 */
.risk-high {
  border: 1px solid #fd4b4f;
  background: rgba(239,100,99,.05);
}

.risk-high.active {
  background: rgba(239,100,99,.2);
  -webkit-animation: bounce-in-data-v 1s;
  animation: bounce-in-data-v 1s;
}

.risk-medium {
  border: 1px solid #f5a53d;
  background: rgba(255,189,26,.05);
}

.risk-medium.active {
  background: rgba(255,189,26,.2);
  -webkit-animation: bounce-in-data-v 1s;
  animation: bounce-in-data-v 1s;
}

.risk-low {
  border: 1px solid #4083f7;
  background: rgba(27,205,202,.05);
}

.risk-low.active {
  background: rgba(27,205,202,.2);
  -webkit-animation: bounce-in-data-v 1s;
  animation: bounce-in-data-v 1s;
}

/* 特殊风险类型样式 */
.risk-table {
  border: 1px solid #4877ff;
}

.risk-table.active {
  border: 1px solid #4877ff!important;
  box-shadow: 0 5px 16px rgba(72,119,255,.5);
}

/* 风险项激活动画 */
@keyframes bounce-in-data-v {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 确保 PDF 容器样式正确 */
#pdf-container {
  width: 100%;
  height: 100vh;
  overflow: auto;
  background-color: #f0f0f0;
  position: relative;
}

.main-container {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  flex-direction: column;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 控制按钮样式 */
.controls {
  background-color: #f8f8f8;
  border-bottom: 1px solid #ddd;
  padding: 10px 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.control-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.controls button {
  padding: 6px 12px;
  border: 1px solid #ccc;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.controls button:hover {
  background-color: #f0f0f0;
  border-color: #aaa;
}

/* 页码按钮 */
#goto-page-29, #highlight-far-risk-1, #highlight-far-risk-2 {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  font-weight: bold;
}

#goto-page-29:hover, #highlight-far-risk-1:hover, #highlight-far-risk-2:hover {
  background-color: #bae7ff;
  border-color: #69c0ff;
}

/* PDF页面指示器样式 */
.pdf-page-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.pdf-page-indicator:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* 页码输入框容器 */
.pdf-page-input-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  z-index: 1100;
}

.pdf-page-input-container form {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pdf-page-input-container input {
  width: 60px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  text-align: center;
}

.pdf-page-input-container button {
  padding: 8px 15px;
  background-color: #4083f7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.pdf-page-input-container button:hover {
  background-color: #3371e3;
} 