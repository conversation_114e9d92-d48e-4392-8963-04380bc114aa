# 百胜 PDF 合同审查系统

一个功能强大的 PDF 合同查看与风险标记系统，支持在线浏览 PDF 文档、标记风险点、风险高亮展示等功能。

## 功能特点

- 高性能 PDF 渲染与展示
- 智能内存管理与资源优化
- 风险项标记与高亮显示
- **风险等级筛选与分类显示**
- 分页导航与页面指示器
- 懒加载与虚拟滚动
- 键盘导航支持（上下翻页、跳转）
- 支持高 DPI 屏幕显示
- 全面的浏览器兼容性（包括旧版 Chrome）

## 技术栈

- TypeScript
- PDF.js (v5.2.133)
- Vite
- ES Modules
- CSS3

## 系统要求

- Node.js 14 或更高版本
- 支持的浏览器: 
  - Chrome 52+（包括解决的 Chrome 121 兼容性问题）
  - Firefox 最新版
  - Safari 最新版
  - Edge 最新版

## 安装指南

1. 克隆仓库
```bash
git clone [仓库地址]
cd pdf-contract-review
```

2. 安装依赖
```bash
npm install
# 或使用 pnpm
pnpm install
```

3. 启动开发服务器
```bash
npm run dev
# 或使用 pnpm
pnpm dev
```

4. 构建生产版本
```bash
npm run build
# 或使用 pnpm
pnpm build
```

## 使用说明

### 基本用法

```javascript
// 创建 PDF 查看器实例
const pdfViewer = new PDFViewer('pdf文件路径', 'container-id');

// 设置风险项
pdfViewer.setRisks([
  {
    objectId: 'risk-1',
    name: '合同风险1',
    riskLevel: '1', // 0-提取要素，1-高风险，2-中风险，3-低风险
    type: '欺诈风险',
    anchors: [
      {
        content: '风险描述',
        pageIndex: 1, // 从1开始的页码索引
        points: [[x1, y1, x2, y2]] // 风险区域坐标
      }
    ]
  },
  // 更多风险项...
]);

// 跳转到指定页码
pdfViewer.goToPage(5);

// 高亮显示指定风险项
pdfViewer.handleRiskMenuClick('risk-1');

// 风险等级筛选功能
pdfViewer.showRisksByLevels(['1', '2']); // 只显示高风险和中风险
pdfViewer.showRisksByLevels(['0']);      // 只显示提取要素
pdfViewer.showAllRisks();                // 显示所有风险项

// 获取当前筛选状态
const currentLevels = pdfViewer.getVisibleRiskLevels();

// 销毁实例，释放资源
pdfViewer.dispose();
```

### 处理复杂 URL 的示例

以下示例展示如何加载带有复杂查询参数的远程 PDF：

```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>PDF 合同审查</title>
  <style>
    body, html { 
      margin: 0; 
      padding: 0; 
      height: 100%; 
    }
    #pdf-container {
      width: 100%;
      height: 100vh;
      overflow: auto;
    }
  </style>
</head>
<body>
  <div id="pdf-container"></div>
  
  <script type="module">
    import { PDFViewer } from './dist/static/js/main.js';
    
    document.addEventListener('DOMContentLoaded', () => {
      // 复杂的 PDF URL 带有多个查询参数
      const pdfUrl = 'http://example.com/api/file/view/12345.pdf?t=1747806990096&type=0&s=face2699e531203d4f4b85806f46e979';
      
      // 创建 PDF 查看器实例
      const pdfViewer = new PDFViewer(pdfUrl, 'pdf-container');
      
      // 处理加载错误
      window.addEventListener('error', function(event) {
        if (event.message.includes('URL.parse')) {
          console.error('检测到 URL.parse 错误，请确保 polyfill 已正确加载');
        }
      });
      
      // 将实例添加到 window 对象，便于调试
      window.pdfViewer = pdfViewer;
    });
  </script>
</body>
</html>
```

### 风险等级筛选

系统支持按风险等级筛选显示风险项：

#### API 方法

```javascript
// 显示指定等级的风险项
pdfViewer.showRisksByLevels(['1', '2']); // 显示高风险和中风险
pdfViewer.showRisksByLevels(['0']);      // 只显示提取要素
pdfViewer.showRisksByLevels(['3']);      // 只显示低风险

// 显示所有风险项
pdfViewer.showAllRisks();

// 获取当前筛选状态
const visibleLevels = pdfViewer.getVisibleRiskLevels(); // 返回 string[] | null
```

#### 风险等级说明

- `'0'`: 提取要素（紫色边框）
- `'1'`: 高风险（红色边框）
- `'2'`: 中风险（橙色边框）
- `'3'`: 低风险（蓝色边框）

#### UI 控制按钮

系统提供了便捷的 UI 按钮进行风险筛选：

- **单个等级筛选**：提取要素、高风险、中风险、低风险、显示全部
- **组合筛选**：高+中风险、提取+高风险
- **状态指示**：当前激活的筛选按钮会高亮显示

### 键盘导航

- `左箭头`/`PageUp`: 上一页
- `右箭头`/`PageDown`: 下一页
- `Home`: 跳转到第一页
- `End`: 跳转到最后一页

## 浏览器兼容性说明

本项目特别解决了 Chrome 121 及以下版本中 `URL.parse` 不支持的问题，通过引入自定义 polyfill 实现了完全兼容。如果在旧版浏览器中遇到问题，请确保：

1. polyfill 正确加载
2. 使用构建后的生产版本

### URL.parse Polyfill 实现说明

为了解决 Chrome 121 及更早版本浏览器中不支持 PDF.js 使用的 `URL.parse` 方法的问题，我们实现了一个全局 polyfill：

```typescript
// polyfills/url-parse-polyfill.ts
if (!('parse' in URL)) {
  (URL as any).parse = function(urlStr: string, baseUrl?: string | URL): URL {
    try {
      // 使用原生 URL 构造函数创建 URL 对象
      return new URL(urlStr, baseUrl);
    } catch (error) {
      console.warn('URL.parse polyfill: 创建 URL 对象失败', error);
      
      // 如果创建失败，返回基本的 URL 对象模拟
      const fallbackUrl = {
        href: urlStr,
        protocol: '',
        hostname: '',
        host: '',
        pathname: urlStr,
        search: '',
        hash: '',
        toString: function() { return this.href; }
      };
      
      return fallbackUrl as unknown as URL;
    }
  };
}
```

这个 polyfill 需要在应用启动时第一时间加载，确保在 PDF.js 库调用 `URL.parse` 之前就完成注册：

```typescript
// main.ts
// 首先导入 URL.parse polyfill，确保它在任何其他模块之前加载
import './polyfills/url-parse-polyfill';

// 然后导入其他模块
import { PDFViewer } from "./ts/pdf-viewer";
```

## 项目结构

```
pdf-contract-review/
├── dist/               # 构建输出目录
├── public/             # 静态资源
│   └── pdf.pdf         # 示例 PDF 文件
├── src/
│   ├── css/            # 样式文件
│   ├── polyfills/      # 浏览器兼容性 polyfill
│   │   ├── url-parse-polyfill.ts  # URL.parse polyfill
│   │   └── url-parse.d.ts         # 类型声明
│   ├── ts/             # TypeScript 源码
│   │   └── pdf-viewer.ts          # PDF 查看器核心实现
│   ├── types/          # 类型定义
│   │   └── pdf-risk.ts            # 风险项类型定义
│   └── main.ts         # 应用入口
├── index.html          # HTML 入口
├── package.json        # 项目配置
├── tsconfig.json       # TypeScript 配置
└── vite.config.ts      # Vite 构建配置
```

## 开发指南

### 内存优化

PDF 渲染占用资源较多，系统实现了以下优化机制：

- 页面懒加载：仅渲染可见页面
- 智能清理：当内存压力大时自动卸载不可见页面
- 资源控制：监控内存使用并主动释放

### 风险项定制

风险项支持以下级别，对应不同的高亮颜色：

- `'0'`: 提取要素（紫色边框，带"提取"标签）
- `'1'`/`'high'`: 高风险（红色边框）
- `'2'`/`'medium'`: 中风险（橙色边框）
- `'3'`/`'low'`: 低风险（蓝色边框）

#### 风险项样式类

系统为不同风险等级提供了对应的 CSS 类：

```css
.risk-extract  /* 提取要素 - 紫色 */
.risk-high     /* 高风险 - 红色 */
.risk-medium   /* 中风险 - 橙色 */
.risk-low      /* 低风险 - 蓝色 */
```

#### 筛选功能特性

- **实时筛选**：修改筛选条件后立即重新渲染页面
- **状态保持**：设置新风险项时保持当前筛选状态
- **性能优化**：只重新渲染已加载的页面
- **组合筛选**：支持同时显示多个风险等级

## 常见问题

### PDF 加载失败

如果 PDF 加载失败，请检查：

1. PDF 文件路径是否正确
2. PDF 文件是否可访问
3. 浏览器是否支持跨域请求（如果 PDF 在不同域）
4. 浏览器控制台是否有详细错误信息

### 旧版浏览器兼容性问题

对于 Chrome 121 及以下版本中可能出现的 `URL.parse is not a function` 错误，我们已经通过 polyfill 解决。如果依然遇到问题，请确保：

1. 项目已正确构建
2. polyfill 在应用初始化时被加载
3. 使用的是最新版本的代码

## 许可证

[添加许可证信息] 