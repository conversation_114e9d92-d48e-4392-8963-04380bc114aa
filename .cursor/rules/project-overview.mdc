---
description:
globs:
alwaysApply: false
---
# PDF Contract Review Application

This application is a PDF contract review tool built with Vite, TypeScript and PDF.js.

## Core Files
- Entry point: [index.html](mdc:index.html) which loads [src/main.ts](mdc:src/main.ts)
- PDF viewer component: [src/ts/pdf-viewer.ts](mdc:src/ts/pdf-viewer.ts)

## Tech Stack
- Build tool: Vite
- Language: TypeScript
- PDF Parsing: PDF.js (via pdfjs-dist package)

## Project Structure
- `/src/ts/` - TypeScript source files
- `/src/css/` - CSS styles
- `/public/` - Static assets
