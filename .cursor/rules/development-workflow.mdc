---
description: 
globs: 
alwaysApply: false
---
# Development Workflow

## Getting Started

1. Install dependencies:
```bash
pnpm install
```

2. Start the development server:
```bash
pnpm dev
```

3. Access the application at http://localhost:3005

## Project Structure

- [index.html](mdc:index.html): Main HTML entry point
- [src/main.ts](mdc:src/main.ts): TypeScript entry point
- [src/ts/pdf-viewer.ts](mdc:src/ts/pdf-viewer.ts): PDF rendering component

## Adding Features

When implementing new features:

1. Follow TypeScript's strict typing requirements
2. Properly initialize all class properties
3. Use type imports where appropriate
4. Update component documentation

## Building for Production

```bash
pnpm build
```

The output will be in the `dist` directory, configured to work even when deployed in a subdirectory (due to `base: './'` in the Vite config).
