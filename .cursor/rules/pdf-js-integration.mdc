---
description: 
globs: 
alwaysApply: false
---
# PDF.js Integration

This project uses [PDF.js](mdc:https:/mozilla.github.io/pdf.js) (via the [pdfjs-dist](mdc:https:/www.npmjs.com/package/pdfjs-dist) package) for PDF rendering capabilities.

## Worker Configuration

PDF.js requires a worker script for processing PDFs. This is configured in [src/ts/pdf-viewer.ts](mdc:src/ts/pdf-viewer.ts):

```typescript
GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.mjs',
  import.meta.url
).toString();
```

## Key PDF.js Modules

- `getDocument`: Loads a PDF document
- `PDFDocumentProxy`: Represents a loaded PDF document
- `GlobalWorkerOptions`: Configures the PDF.js worker

## Type Imports

When importing PDF.js types, they must use the `type` keyword due to the project's TypeScript configuration:

```typescript
import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist';
import type { PDFDocumentProxy } from 'pdfjs-dist';
```
