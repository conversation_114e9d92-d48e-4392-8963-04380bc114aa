---
description:
globs:
alwaysApply: false
---
# PDF Viewer Component

The [PDF Viewer](mdc:src/ts/pdf-viewer.ts) is the core component responsible for rendering PDF documents.

## Usage

The PDF viewer is initialized with two parameters:
- `pdfPath`: Path to the PDF file to be displayed
- `containerId`: ID of the HTML container element where the PDF will be rendered

## Implementation Details

- Uses [pdfjs-dist](https://www.npmjs.com/package/pdfjs-dist) for PDF rendering
- Configures the PDF.js worker properly for browser compatibility
- Handles rendering of PDF pages within the specified container

## Development Notes

- The component should be enhanced to support:
  - Page navigation
  - Zoom controls
  - Text selection
  - Annotations
