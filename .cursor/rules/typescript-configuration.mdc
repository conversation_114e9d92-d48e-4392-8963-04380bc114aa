---
description: 
globs: 
alwaysApply: false
---
# TypeScript Configuration

The project uses TypeScript with strict type checking enabled. Key configuration details:

## Configuration File

The [tsconfig.json](mdc:tsconfig.json) contains the following important settings:

- `verbatimModuleSyntax: true`: Requires using `type` imports for types
- `strict: true`: Enables all strict type checking options
- `target: "ES2020"`: Targets ES2020 JavaScript features
- `moduleResolution: "bundler"`: Uses the bundler module resolution strategy

## Type Imports

Due to `verbatimModuleSyntax: true`, type-only imports must be explicitly marked:

```typescript
// Incorrect
import { SomeType } from 'some-module';

// Correct
import type { SomeType } from 'some-module';
```

## Class Properties

Under strict mode, all class properties must be initialized:

```typescript
// Will cause an error
class Example {
  private property: string;
}

// Correct approaches
class Example {
  private property: string = "default";
  // OR
  private property: string;
  
  constructor() {
    this.property = "initialized";
  }
}
```
