---
description: 
globs: 
alwaysApply: false
---
# Vite Build System

This project uses [Vite](mdc:https:/vitejs.dev) for development and building.

## Configuration

The [vite.config.ts](mdc:vite.config.ts) file contains important configurations:

- Uses the legacy plugin for broader browser compatibility
- Optimizes PDF.js dependencies
- Configures output directory structure
- Enables source maps for debugging
- Sets up development server on port 3005

## Scripts

Available npm scripts in [package.json](mdc:package.json):

- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run build:dev`: Build with development settings
- `npm run preview`: Preview the production build

## Legacy Browser Support

The project is configured to support older browsers including IE11 and Chrome 52 through the legacy plugin.
