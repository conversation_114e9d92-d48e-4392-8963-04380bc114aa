<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PDF Contract Review</title>
    <style>
        body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .main-container {
        display: flex;
        width: 100%;
        height: 100vh;
        overflow: hidden;
        flex-direction: column;
        }
        /* 控制按钮样式 */
        .controls {
        background-color: #f8f8f8;
        border-bottom: 1px solid #ddd;
        padding: 10px 15px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .control-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        }

        .controls button {
        padding: 6px 12px;
        border: 1px solid #ccc;
        background-color: #fff;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s ease;
        }

        .controls button:hover {
        background-color: #f0f0f0;
        border-color: #aaa;
        }

        /* 页码按钮 */
        #goto-page-29, #highlight-test-risk {
        background-color: #e6f7ff;
        border-color: #91d5ff;
        font-weight: bold;
        }

        #goto-page-29:hover, #highlight-test-risk:hover {
        background-color: #bae7ff;
        border-color: #69c0ff;
        }

        /* 风险等级筛选按钮样式 */
        .risk-filter-btn {
        position: relative;
        }

        .risk-filter-btn.active {
        background-color: #f6ffed;
        border-color: #b7eb8f;
        color: #52c41a;
        font-weight: bold;
        }

        .risk-filter-btn.active:hover {
        background-color: #d9f7be;
        border-color: #95de64;
        }

        /* 风险等级按钮特定颜色 */
        .risk-extract-btn.active {
        background-color: #f9f0ff;
        border-color: #d3adf7;
        color: #9c27b0;
        }

        .risk-high-btn.active {
        background-color: #fff2f0;
        border-color: #ffccc7;
        color: #ff4d4f;
        }

        .risk-medium-btn.active {
        background-color: #fff7e6;
        border-color: #ffd591;
        color: #fa8c16;
        }

        .risk-low-btn.active {
        background-color: #f6ffed;
        border-color: #b7eb8f;
        color: #52c41a;
        }

    </style>
    <link rel="stylesheet" href="/src/css/style.css">
  </head>
  <body>
    <div class="main-container">
        <div id="pdf-container"></div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <script type="text/javascript">
      // 创建单条测试数据
      const generateTestRisks = () => {
          const risks = [];

          // 只添加一条测试数据
          risks.push({
              objectId: 'test_risk_1',
              name: '测试风险项',
              code: 'TEST01',
              riskLevel: '1', // 高风险
              type: '测试风险',
              anchors: [
                  {
                      content: '第2页的测试风险条款',
                      pageIndex: 2, // 第2页
                      points: [
                          [201.85416082763672, 142.36560412597657, 234.00792095947267, 153.31680444335936]
                      ]
                  }
              ]
          });

          return risks;
      };

      // 等待 PDFViewer 类加载完成
      document.addEventListener('DOMContentLoaded', async () => {
            window.addEventListener('blur', () => {
            console.log('窗口失去焦点，可能切换回原窗口或最小化');

            });

            document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                console.log('页面不可见');

            }
            });
          try {
              // 检查浏览器兼容性
              if (!window.isBrowserCompatible) {
                  console.log('浏览器版本不兼容，跳过PDF查看器初始化');
                  return;
              }

              // 确保 PDFViewer 类已加载
              if (!window.PDFViewer) {
                  console.error('PDFViewer 类尚未加载');
                  return;
              }

              // 生成测试数据
              const testRisks = generateTestRisks();
              
              // 初始化 PDF 查看器
              const pdfPath = './pdf.pdf';  // public 目录中的 PDF 文件
              const containerId = 'pdf-container';
              
              // 创建 PDF 查看器实例
              const pdfViewer = new window.PDFViewer(pdfPath, containerId);
              
              // 设置风险数据
              setTimeout(() => {
                  pdfViewer.setRisks(testRisks);
                  console.log(`风险数据加载成功，共 ${testRisks.length} 个风险项`);
              }, 2000); // 等待 PDF 加载完成后再设置风险数据
              
              console.log('PDF 查看器初始化成功');
              
              // 添加控制按钮
              const mainContainer = document.querySelector('.main-container');
              if (mainContainer) {
                  const controlsDiv = document.createElement('div');
                  controlsDiv.className = 'controls';
                  controlsDiv.innerHTML = `
                      <div class="control-group">
                          <button id="goto-page-1">第1页</button>
                          <button id="goto-page-5">第5页</button>
                          <button id="goto-page-10">第10页</button>
                          <button id="goto-page-15">第15页</button>
                          <button id="goto-page-20">第20页</button>
                          <button id="goto-page-25">第25页</button>
                          <button id="goto-page-29">第29页</button>
                      </div>
                      <div class="control-group">
                          <button id="highlight-test-risk">高亮测试风险项</button>
                      </div>
                      <div class="control-group">
                          <strong style="margin-right: 10px; align-self: center;">风险筛选:</strong>
                          <button id="show-all-risks" class="risk-filter-btn active">显示全部</button>
                          <button id="show-extract-risks" class="risk-filter-btn risk-extract-btn" data-level="0">提取要素</button>
                          <button id="show-high-risks" class="risk-filter-btn risk-high-btn" data-level="1">高风险</button>
                          <button id="show-medium-risks" class="risk-filter-btn risk-medium-btn" data-level="2">中风险</button>
                          <button id="show-low-risks" class="risk-filter-btn risk-low-btn" data-level="3">低风险</button>
                      </div>
                      <div class="control-group">
                          <strong style="margin-right: 10px; align-self: center;">组合筛选:</strong>
                          <button id="show-high-medium-risks" class="risk-filter-btn">高+中风险</button>
                          <button id="show-extract-high-risks" class="risk-filter-btn">提取+高风险</button>
                      </div>
                  `;
                  
                  mainContainer.insertBefore(controlsDiv, mainContainer.firstChild);
                  
                  // 添加页面跳转按钮事件
                  [1, 5, 10, 15, 20, 25, 29].forEach(pageNum => {
                      document.getElementById(`goto-page-${pageNum}`)?.addEventListener('click', () => {
                          pdfViewer.goToPage(pageNum);
                      });
                  });
                  
                  // 添加测试风险高亮按钮事件
                  document.getElementById('highlight-test-risk')?.addEventListener('click', () => {
                      pdfViewer.handleRiskMenuClick('test_risk_1');
                  });

                  // 添加风险筛选按钮事件
                  const updateFilterButtonStates = (activeButtonId) => {
                      // 移除所有按钮的 active 状态
                      document.querySelectorAll('.risk-filter-btn').forEach(btn => {
                          btn.classList.remove('active');
                      });
                      // 为当前按钮添加 active 状态
                      document.getElementById(activeButtonId)?.classList.add('active');
                  };

                  // 显示全部风险
                  document.getElementById('show-all-risks')?.addEventListener('click', () => {
                      pdfViewer.showAllRisks();
                      updateFilterButtonStates('show-all-risks');
                      console.log('显示全部风险项');
                  });

                  // 单个等级筛选
                  document.getElementById('show-extract-risks')?.addEventListener('click', () => {
                      pdfViewer.showRisksByLevels(['0']);
                      updateFilterButtonStates('show-extract-risks');
                      console.log('只显示提取要素');
                  });

                  document.getElementById('show-high-risks')?.addEventListener('click', () => {
                      pdfViewer.showRisksByLevels(['1']);
                      updateFilterButtonStates('show-high-risks');
                      console.log('只显示高风险');
                  });

                  document.getElementById('show-medium-risks')?.addEventListener('click', () => {
                      pdfViewer.showRisksByLevels(['2']);
                      updateFilterButtonStates('show-medium-risks');
                      console.log('只显示中风险');
                  });

                  document.getElementById('show-low-risks')?.addEventListener('click', () => {
                      pdfViewer.showRisksByLevels(['3']);
                      updateFilterButtonStates('show-low-risks');
                      console.log('只显示低风险');
                  });

                  // 组合筛选
                  document.getElementById('show-high-medium-risks')?.addEventListener('click', () => {
                      pdfViewer.showRisksByLevels(['1', '2']);
                      updateFilterButtonStates('show-high-medium-risks');
                      console.log('显示高风险和中风险');
                  });

                  document.getElementById('show-extract-high-risks')?.addEventListener('click', () => {
                      pdfViewer.showRisksByLevels(['0', '1']);
                      updateFilterButtonStates('show-extract-high-risks');
                      console.log('显示提取要素和高风险');
                  });
              }
              
              // 将 pdfViewer 实例添加到 window 对象，方便在控制台中访问
              window.pdfViewer = pdfViewer;
              
              // 监听风险项选中事件
              document.addEventListener('riskSelected', (event) => {
                  const { riskId, risk } = event.detail;
                  console.log('风险项被选中:', riskId, risk);
                  // 在这里可以添加你的自定义处理逻辑
              });
              
              // 监听风险项点击事件
              document.addEventListener('riskClicked', (event) => {
                  const { riskId, risk } = event.detail;
                  console.log('风险项被点击:', riskId, risk);
                  // 在这里可以添加你的自定义处理逻辑
              });
              
          } catch (error) {
              console.error('PDF 查看器初始化失败:', error);
              const container = document.getElementById('pdf-container');
              if (container) {
                  container.innerHTML = `
                      <div class="pdf-error">
                          <h3>PDF 查看器初始化失败</h3>
                          <p>错误详情: ${error.message || String(error)}</p>
                      </div>
                  `;
              }
          }
      });
    </script>
  </body>
</html>
